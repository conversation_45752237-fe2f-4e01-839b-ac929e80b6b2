# 性能测试执行计划

## 1. 测试环境准备

### 1.1 硬件环境

- 测试服务器配置：
  - CPU: 4核或以上
  - 内存: 8GB或以上
  - 存储: SSD硬盘
  - 网络: 千兆网络

### 1.2 软件环境

- 操作系统: Linux (推荐Ubuntu 20.04或CentOS 7)
- Go版本: 1.19或以上
- MySQL: 8.0或以上
- Redis: 6.0或以上
- MongoDB: 4.4或以上

### 1.3 监控工具安装

```bash
# 安装Prometheus
wget https://github.com/prometheus/prometheus/releases/download/v2.37.0/prometheus-2.37.0.linux-amd64.tar.gz
tar xvfz prometheus-2.37.0.linux-amd64.tar.gz
cd prometheus-2.37.0.linux-amd64

# 安装Grafana
wget https://dl.grafana.com/oss/release/grafana-9.0.0.linux-amd64.tar.gz
tar -zxvf grafana-9.0.0.linux-amd64.tar.gz
cd grafana-9.0.0

# 安装wrk
git clone https://github.com/wg/wrk.git
cd wrk
make
sudo cp wrk /usr/local/bin/
```

## 2. 应用部署

### 2.1 配置文件准备

```yaml
# config-test.yaml
service:
  name: 'app_service'
  version: '1.0.0'
  address: ":8801"
  token: "test_token"
  env: "test"

mysql:
  app_service:
    host: '127.0.0.1'
    port: 3306
    name: 'app_service_test'
    user: 'test_user'
    password: 'test_password'
    prefix: ''
    loc: Asia%2FShanghai

redis:
  app_service:
    host: '127.0.0.1'
    port: 6379
    user: ''
    password: ''
    db: 0

mongo:
  uri: '127.0.0.1:27017/?directConnection=true'
  database: 'admin'
  user: 'test_user'
  password: 'test_password'
  timeout: 20
  maxnum: 100
```

### 2.2 启动应用

```bash
# 编译应用
go build -o app_service cmd/http_server/main.go

# 启动应用
ENV=test ./app_service
```

## 3. 测试数据准备

### 3.1 创建测试用户

```sql
-- 创建测试用户
INSERT INTO users (id, username, password, created_at, updated_at) VALUES
('user_001', 'test_merchant_1', 'password_hash_1', NOW(), NOW()),
('user_002', 'test_merchant_2', 'password_hash_2', NOW(), NOW());
-- 继续创建更多测试用户...
```

### 3.2 创建测试帖子

```sql
-- 创建测试帖子
INSERT INTO posts (id, merchant_id, description, price, status, created_at, updated_at) VALUES
('post_001', 'user_001', 'Test post 1', 1000, 1, NOW(), NOW()),
('post_002', 'user_001', 'Test post 2', 2000, 1, NOW(), NOW());
-- 继续创建更多测试帖子...
```

## 4. 性能测试执行

### 4.1 单接口性能测试

```bash
# 测试创建帖子接口
wrk -t4 -c1 -d60s -T30s \
  --header "Content-Type: application/json" \
  --header "Authorization: Bearer test_token" \
  -s scripts/create_post.lua \
  http://localhost:8801/web/v1/posts/create

# 测试获取帖子详情接口
wrk -t4 -c1 -d60s -T30s \
  --header "Authorization: Bearer test_token" \
  http://localhost:8801/web/v1/posts/detail?id=post_001

# 测试获取帖子列表接口
wrk -t4 -c1 -d60s -T30s \
  --header "Authorization: Bearer test_token" \
  http://localhost:8801/web/v1/posts/list?page=1&size=20
```

### 4.2 并发用户数测试

```bash
# 10并发用户测试
wrk -t4 -c10 -d60s -T30s \
  --header "Authorization: Bearer test_token" \
  http://localhost:8801/web/v1/posts/list?page=1&size=20

# 50并发用户测试
wrk -t4 -c50 -d60s -T30s \
  --header "Authorization: Bearer test_token" \
  http://localhost:8801/web/v1/posts/list?page=1&size=20

# 100并发用户测试
wrk -t4 -c100 -d60s -T30s \
  --header "Authorization: Bearer test_token" \
  http://localhost:8801/web/v1/posts/list?page=1&size=20

# 200并发用户测试
wrk -t4 -c200 -d60s -T30s \
  --header "Authorization: Bearer test_token" \
  http://localhost:8801/web/v1/posts/list?page=1&size=20

# 500并发用户测试
wrk -t4 -c500 -d60s -T30s \
  --header "Authorization: Bearer test_token" \
  http://localhost:8801/web/v1/posts/list?page=1&size=20
```

### 4.3 长时间运行稳定性测试

```bash
# 100并发用户，持续1小时
wrk -t4 -c100 -d1h -T30s \
  --header "Authorization: Bearer test_token" \
  http://localhost:8801/web/v1/posts/list?page=1&size=20
```

### 4.4 高负载压力测试

```bash
# 逐步增加并发用户数直到系统达到性能瓶颈
# 从100并发开始，每次增加100，并发直到响应时间超过阈值或错误率超过1%
wrk -t4 -c100 -d60s -T30s \
  --header "Authorization: Bearer test_token" \
  http://localhost:8801/web/v1/posts/list?page=1&size=20
```

## 5. 数据收集

### 5.1 wrk测试结果收集

每次测试后记录以下数据：
- 请求总数
- 请求成功率
- 平均响应时间
- 95%响应时间
- 99%响应时间
- 最大响应时间
- 每秒请求数(QPS)

### 5.2 系统监控数据收集

使用Prometheus和Grafana收集以下数据：
- CPU使用率
- 内存使用率
- 磁盘IO
- 网络IO
- MySQL连接数
- Redis连接数
- Goroutine数量

### 5.3 数据库性能数据收集

- MySQL慢查询日志
- 查询响应时间
- 连接数使用情况

### 5.4 缓存性能数据收集

- Redis命中率
- Redis内存使用情况
- Redis连接数

## 6. 数据分析

### 6.1 性能基线确定

根据单接口性能测试结果确定系统性能基线。

### 6.2 并发性能分析

分析不同并发用户数下的系统表现，确定系统最佳并发数。

### 6.3 稳定性分析

分析长时间运行测试结果，检查是否存在内存泄漏等问题。

### 6.4 瓶颈识别

根据高负载测试结果识别系统性能瓶颈。

## 7. 测试脚本

### 7.1 create_post.lua

```lua
-- 创建帖子的wrk脚本
request = function()
   local headers = {}
   headers["Content-Type"] = "application/json"
   headers["Authorization"] = "Bearer test_token"
   
   local body = '{"description": "Performance test post", "price": 1000, "media_files": []}'
   
   return wrk.format("POST", wrk.path, headers, body)
end
```

## 8. 注意事项

### 8.1 测试环境隔离

确保测试环境与生产环境隔离，避免测试数据污染生产数据。

### 8.2 监控工具配置

确保所有监控工具正确配置并运行，以便收集准确的性能数据。

### 8.3 测试数据清理

每次测试后清理测试数据，确保下一次测试的准确性。

### 8.4 结果记录

详细记录每次测试的结果，包括测试参数、测试结果和环境信息。