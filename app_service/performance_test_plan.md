# API性能测试计划

## 1. 测试目标

- 识别系统性能瓶颈
- 确定系统性能基线
- 为系统优化提供数据支持
- 验证优化措施的效果

## 2. 测试范围

### 2.1 关键API接口

- 帖子相关接口：
  - POST /web/v1/posts/create (创建帖子)
  - GET /web/v1/posts/detail (获取帖子详情)
  - GET /web/v1/posts/list (获取帖子列表)
  - GET /web/v1/posts/my (获取我的帖子列表)
  - PUT /web/v1/posts/edit (编辑帖子)
  - PUT /web/v1/posts/status (更新帖子状态)
  - DELETE /web/v1/posts/delete (删除帖子)

- 消息相关接口：
  - GET /web/v1/messages/list (获取消息列表)
  - POST /web/v1/messages/send (发送消息)

### 2.2 性能维度

- 响应时间
- 吞吐量(QPS)
- 并发用户数
- 错误率
- 资源使用率(CPU、内存、数据库连接数等)

## 3. 测试工具

### 3.1 HTTP压力测试工具

- wrk: 高性能HTTP基准测试工具
- ab (Apache Bench): Apache服务器自带的压力测试工具

### 3.2 性能分析工具

- pprof: Go语言内置的性能分析工具
- go-torch: 生成火焰图的pprof可视化工具

### 3.3 监控工具

- Prometheus: 时间序列数据库，用于收集和存储监控数据
- Grafana: 数据可视化工具，用于展示监控数据
- MySQL慢查询日志分析
- Redis性能监控

## 4. 测试环境

### 4.1 环境配置

- 开发环境：本地开发环境
- 测试环境：专用测试服务器
- 预生产环境：与生产环境配置一致的环境

### 4.2 硬件配置

- CPU: 至少4核
- 内存: 至少8GB
- 磁盘: SSD存储

## 5. 测试场景

### 5.1 单接口性能测试

- 目标：测试单个接口在无并发情况下的响应时间和吞吐量
- 并发用户数：1
- 测试时长：60秒
- 测试次数：3次，取平均值

### 5.2 并发用户数测试

- 目标：测试系统在不同并发用户数下的性能表现
- 并发用户数：10, 50, 100, 200, 500
- 测试时长：60秒
- 测试次数：3次，取平均值

### 5.3 长时间运行稳定性测试

- 目标：测试系统在长时间运行下的稳定性
- 并发用户数：100
- 测试时长：1小时
- 监控指标：内存泄漏、CPU使用率、错误率

### 5.4 高负载压力测试

- 目标：测试系统在极限负载下的表现
- 并发用户数：逐步增加至系统响应时间超过阈值或错误率超过1%
- 测试时长：60秒
- 监控指标：系统崩溃点、响应时间、错误率

## 6. 性能指标

### 6.1 响应时间

- 平均响应时间
- 95%响应时间
- 99%响应时间
- 最大响应时间

### 6.2 吞吐量

- 每秒请求数(QPS)
- 每秒事务数(TPS)

### 6.3 错误率

- HTTP错误率
- 业务错误率

### 6.4 资源使用率

- CPU使用率
- 内存使用率
- 数据库连接数
- Redis连接数

## 7. 测试步骤

### 7.1 准备阶段

1. 搭建测试环境
2. 准备测试数据
3. 配置监控工具
4. 确认测试工具可用

### 7.2 执行阶段

1. 执行单接口性能测试
2. 执行并发用户数测试
3. 执行长时间运行稳定性测试
4. 执行高负载压力测试

### 7.3 分析阶段

1. 收集性能数据
2. 分析性能瓶颈
3. 生成测试报告

## 8. 测试数据准备

### 8.1 用户数据

- 创建1000个测试用户
- 确保用户具有商家身份

### 8.2 帖子数据

- 创建10000个测试帖子
- 包含不同状态的帖子(上架、下架、违规、删除)

### 8.3 消息数据

- 创建100000条测试消息
- 包含不同类型的消息(文本、图片、帖子快照等)

## 9. 监控指标配置

### 9.1 API监控

- 接口响应时间
- 接口QPS
- 接口错误率

### 9.2 系统监控

- CPU使用率
- 内存使用率
- 磁盘IO
- 网络IO

### 9.3 数据库监控

- MySQL连接数
- 慢查询数量
- 查询响应时间

### 9.4 缓存监控

- Redis连接数
- 缓存命中率
- 缓存响应时间

## 10. 风险评估

### 10.1 数据风险

- 测试数据污染生产环境
- 解决方案：使用独立的测试数据库

### 10.2 系统风险

- 性能测试导致系统崩溃
- 解决方案：在测试环境中进行，逐步增加负载

### 10.3 网络风险

- 网络带宽限制影响测试结果
- 解决方案：在局域网内进行测试

## 11. 测试报告

### 11.1 报告内容

- 测试概述
- 测试环境
- 测试结果
- 性能瓶颈分析
- 优化建议
- 结论

### 11.2 报告格式

- Markdown格式
- 包含图表和数据
- 提供可复现的测试步骤